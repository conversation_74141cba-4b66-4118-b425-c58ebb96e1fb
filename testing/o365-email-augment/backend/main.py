import os
import logging
from typing import List, Optional
from fastapi import FastAP<PERSON>, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, EmailStr
import msal
import requests

# Environment variables
TENANT_ID = os.environ.get("TENANT_ID")
API_CLIENT_ID = os.environ.get("API_CLIENT_ID")
API_CLIENT_SECRET = os.environ.get("API_CLIENT_SECRET")
FRONTEND_URL = os.environ.get("FRONTEND_URL", "http://localhost:3000")

# Microsoft Graph configuration
AUTHORITY = f"https://login.microsoftonline.com/{TENANT_ID}"
GRAPH_SCOPE = ["https://graph.microsoft.com/Mail.Send"]
GRAPH_ENDPOINT = "https://graph.microsoft.com/v1.0/me/sendMail"

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Validate required environment variables
if not all([TENANT_ID, API_CLIENT_ID, API_CLIENT_SECRET]):
    raise ValueError("Missing required environment variables: TENANT_ID, API_CLIENT_ID, API_CLIENT_SECRET")

# FastAPI app initialization
app = FastAPI(
    title="O365 Email Augment API",
    description="Send emails on behalf of authenticated users using Microsoft Graph API",
    version="1.0.0"
)

# CORS middleware configuration
app.add_middleware(
    CORSMiddleware,
    allow_origins=[FRONTEND_URL, "http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "OPTIONS"],
    allow_headers=["*"],
)

# Pydantic models for request/response validation
class EmailRecipient(BaseModel):
    email: EmailStr
    name: Optional[str] = None

class EmailRequest(BaseModel):
    to: List[EmailRecipient]
    subject: str
    body: str
    body_type: str = "Text"  # "Text" or "HTML"
    cc: Optional[List[EmailRecipient]] = None
    bcc: Optional[List[EmailRecipient]] = None

class EmailResponse(BaseModel):
    status: str
    message: str
    details: Optional[str] = None

class HealthResponse(BaseModel):
    status: str
    service: str
    version: str

# Global MSAL client instance
msal_client = None

def get_msal_client():
    """Get or create MSAL confidential client application"""
    global msal_client
    if msal_client is None:
        try:
            msal_client = msal.ConfidentialClientApplication(
                client_id=API_CLIENT_ID,
                client_credential=API_CLIENT_SECRET,
                authority=AUTHORITY
            )
            logger.info("MSAL client initialized successfully")
        except Exception as e:
            logger.error(f"Failed to create MSAL client: {e}")
            raise HTTPException(status_code=500, detail="Authentication service configuration error")
    return msal_client

def build_email_message(email_request: EmailRequest) -> dict:
    """Build Microsoft Graph email message from request"""
    # Build recipients
    to_recipients = [
        {"emailAddress": {"address": recipient.email, "name": recipient.name or recipient.email}}
        for recipient in email_request.to
    ]
    
    # Build email message
    message = {
        "subject": email_request.subject,
        "body": {
            "contentType": email_request.body_type,
            "content": email_request.body
        },
        "toRecipients": to_recipients
    }
    
    # Add CC recipients if provided
    if email_request.cc:
        message["ccRecipients"] = [
            {"emailAddress": {"address": recipient.email, "name": recipient.name or recipient.email}}
            for recipient in email_request.cc
        ]
    
    # Add BCC recipients if provided
    if email_request.bcc:
        message["bccRecipients"] = [
            {"emailAddress": {"address": recipient.email, "name": recipient.name or recipient.email}}
            for recipient in email_request.bcc
        ]
    
    return {
        "message": message,
        "saveToSentItems": "true"
    }

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    return HealthResponse(
        status="healthy",
        service="o365-email-augment-api",
        version="1.0.0"
    )

@app.post("/send-email", response_model=EmailResponse)
async def send_email(email_request: EmailRequest, request: Request):
    """
    Send email on behalf of authenticated user using Microsoft Graph API

    This endpoint expects the frontend to send a token that already has Microsoft Graph permissions.
    No OBO flow is needed when using user consent for both backend API and Graph API.
    """
    try:
        # Extract bearer token from Authorization header
        auth_header = request.headers.get("authorization")
        if not auth_header or not auth_header.lower().startswith("bearer "):
            logger.error("Missing or invalid authorization header")
            raise HTTPException(status_code=401, detail="Missing or invalid bearer token")

        # Extract Graph token from custom header (sent by frontend)
        graph_token_header = request.headers.get("x-graph-token")
        if not graph_token_header:
            logger.error("Missing Microsoft Graph token")
            raise HTTPException(status_code=401, detail="Missing Microsoft Graph token")

        logger.info(f"Attempting to send email with subject: '{email_request.subject}'")
        logger.info("Using direct Graph API token (no OBO flow needed)")

        graph_token = graph_token_header
        
        # Build email message
        mail_data = build_email_message(email_request)
        
        # Send email via Microsoft Graph API
        response = requests.post(
            GRAPH_ENDPOINT,
            headers={
                "Authorization": f"Bearer {graph_token}",
                "Content-Type": "application/json"
            },
            json=mail_data
        )
        
        # Check Graph API response
        if response.status_code >= 300:
            error_detail = response.text
            logger.error(f"Graph API error: {response.status_code} - {error_detail}")
            raise HTTPException(
                status_code=response.status_code,
                detail=f"Failed to send email: {error_detail}"
            )
        
        logger.info("Email sent successfully via Microsoft Graph API")
        return EmailResponse(
            status="success",
            message="Email sent successfully",
            details=f"Email with subject '{email_request.subject}' sent to {len(email_request.to)} recipient(s)"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in send_email: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "O365 Email Augment API",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/health"
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")
