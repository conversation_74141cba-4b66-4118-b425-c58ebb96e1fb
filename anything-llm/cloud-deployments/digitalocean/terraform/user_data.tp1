#!/bin/bash  
# check output of userdata script with sudo tail -f /var/log/cloud-init-output.log 
  
sudo apt-get update  
sudo apt-get install -y docker.io  
sudo usermod -a -G docker ubuntu
  
sudo systemctl enable docker  
sudo systemctl start docker  
  
mkdir -p /home/<USER>
cat <<EOF >/home/<USER>/.env
${env_content}
EOF

sudo docker pull mintplexlabs/anythingllm
sudo docker run -d -p 3001:3001 --cap-add SYS_ADMIN -v /home/<USER>/app/server/storage -v /home/<USER>/.env:/app/server/.env -e STORAGE_DIR="/app/server/storage" mintplexlabs/anythingllm
echo "Container ID: $(sudo docker ps --latest --quiet)"  
  
export ONLINE=$(curl -Is http://localhost:3001/api/ping | head -n 1|cut -d$' ' -f2)  
echo "Health check: $ONLINE"  
echo "Setup complete! AnythingLLM instance is now online!"  
