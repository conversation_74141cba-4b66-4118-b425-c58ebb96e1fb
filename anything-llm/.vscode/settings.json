{"cSpell.words": ["adoc", "aibitat", "AIbitat", "allm", "anythingllm", "<PERSON><PERSON><PERSON>", "Astra", "Chartable", "cleancss", "comkey", "cooldown", "cooldowns", "datafile", "Deduplicator", "Dockerized", "doc<PERSON>", "elevenlabs", "Embeddable", "epub", "fireworksai", "GROQ", "hljs", "huggingface", "inferencing", "kobold<PERSON><PERSON>", "Langchain", "lmstudio", "localai", "mbox", "<PERSON><PERSON><PERSON><PERSON>", "Mintplex", "mixtral", "moderations", "novita", "numpages", "Ollama", "Oobabooga", "openai", "opendocument", "openrouter", "pagerender", "Qdrant", "royalblue", "SearchApi", "searxng", "<PERSON><PERSON>", "Ser<PERSON>ly", "streamable", "textgenwebui", "<PERSON><PERSON>", "Unembed", "uuidv", "vectordbs", "Weaviate", "XAILLM", "<PERSON><PERSON><PERSON>"], "eslint.experimental.useFlatConfig": true, "docker.languageserver.formatter.ignoreMultilineInstructions": true}