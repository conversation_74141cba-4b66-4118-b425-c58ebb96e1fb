#!/usr/bin/env python3
"""
Script to verify backend API permissions are configured correctly
"""
import os
import requests
import msal
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

TENANT_ID = os.environ.get("TENANT_ID")
API_CLIENT_ID = os.environ.get("API_CLIENT_ID")
API_CLIENT_SECRET = os.environ.get("API_CLIENT_SECRET")

def check_app_permissions():
    """Check if the backend API has proper permissions"""
    print("🔍 Checking Backend API Permissions")
    print("=" * 50)
    
    # Create MSAL client
    authority = f"https://login.microsoftonline.com/{TENANT_ID}"
    app = msal.ConfidentialClientApplication(
        client_id=API_CLIENT_ID,
        client_credential=API_CLIENT_SECRET,
        authority=authority
    )
    
    # Try to get an app-only token (this tests if the app has permissions)
    result = app.acquire_token_for_client(scopes=["https://graph.microsoft.com/.default"])
    
    if "access_token" in result:
        print("✅ Backend API can acquire app-only tokens")
        
        # Test Graph API access
        token = result["access_token"]
        headers = {"Authorization": f"Bearer {token}"}
        
        # Try to access Graph API (this will fail if permissions aren't granted)
        response = requests.get(
            "https://graph.microsoft.com/v1.0/applications",
            headers=headers
        )
        
        if response.status_code == 200:
            print("✅ Backend API has proper Graph API access")
        elif response.status_code == 403:
            print("❌ Backend API lacks proper permissions")
            print("   Need to grant admin consent for Microsoft Graph permissions")
        else:
            print(f"⚠️  Unexpected response: {response.status_code}")
            
    else:
        print("❌ Backend API cannot acquire tokens")
        print(f"   Error: {result.get('error')}")
        print(f"   Description: {result.get('error_description')}")
    
    print("\n📋 Configuration Summary:")
    print(f"Tenant ID: {TENANT_ID}")
    print(f"Backend API Client ID: {API_CLIENT_ID}")
    print(f"Authority: {authority}")
    
    print("\n🔧 Required Azure Configuration:")
    print("1. Backend API app registration should have:")
    print("   - Microsoft Graph > Mail.Send (Delegated)")
    print("   - Admin consent granted")
    print("2. Frontend app registration should have:")
    print(f"   - api://{API_CLIENT_ID}/access_as_user (Delegated)")

if __name__ == "__main__":
    if not all([TENANT_ID, API_CLIENT_ID, API_CLIENT_SECRET]):
        print("❌ Missing environment variables")
        print("Please ensure TENANT_ID, API_CLIENT_ID, and API_CLIENT_SECRET are set")
        exit(1)
    
    check_app_permissions()
