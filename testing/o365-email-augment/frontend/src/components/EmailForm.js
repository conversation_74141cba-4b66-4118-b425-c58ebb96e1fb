import React, { useState } from 'react';
import { useMsal } from '@azure/msal-react';
import { Form, Button, Alert, Card, Row, Col } from 'react-bootstrap';
import axios from 'axios';
import { apiConfig } from '../config';
import ConsentHelper from './ConsentHelper';

function EmailForm() {
  const { instance, accounts } = useMsal();
  const [formData, setFormData] = useState({
    to: [{ email: '', name: '' }],
    cc: [],
    bcc: [],
    subject: '',
    body: '',
    body_type: 'Text'
  });
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState({ type: '', text: '' });
  const [showConsentHelper, setShowConsentHelper] = useState(false);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleRecipientChange = (type, index, field, value) => {
    setFormData(prev => ({
      ...prev,
      [type]: prev[type].map((recipient, i) => 
        i === index ? { ...recipient, [field]: value } : recipient
      )
    }));
  };

  const addRecipient = (type) => {
    setFormData(prev => ({
      ...prev,
      [type]: [...prev[type], { email: '', name: '' }]
    }));
  };

  const removeRecipient = (type, index) => {
    setFormData(prev => ({
      ...prev,
      [type]: prev[type].filter((_, i) => i !== index)
    }));
  };

  const getTokens = async () => {
    // Get token for backend API access
    const backendRequest = {
      scopes: [`api://${process.env.REACT_APP_API_CLIENT_ID}/access_as_user`],
      account: accounts[0]
    };

    // Get token for Microsoft Graph access
    const graphRequest = {
      scopes: ["https://graph.microsoft.com/Mail.Send"],
      account: accounts[0]
    };

    try {
      // Try to get both tokens silently first
      const [backendResponse, graphResponse] = await Promise.all([
        instance.acquireTokenSilent(backendRequest),
        instance.acquireTokenSilent(graphRequest)
      ]);

      console.log('Silent token acquisition successful for both APIs');
      return {
        backendToken: backendResponse.accessToken,
        graphToken: graphResponse.accessToken
      };
    } catch (silentError) {
      console.log('Silent token acquisition failed, trying popup with consent...');

      try {
        // If silent fails, try popup with consent for both scopes
        const allScopesRequest = {
          scopes: apiConfig.scopes, // This includes both backend and graph scopes
          account: accounts[0],
          prompt: 'consent',
          forceRefresh: true
        };

        const response = await instance.acquireTokenPopup(allScopesRequest);
        console.log('Popup token acquisition successful:', response);

        // The response should contain a token valid for both scopes
        // We'll use the same token for both (it has multiple audiences)
        return {
          backendToken: response.accessToken,
          graphToken: response.accessToken
        };
      } catch (popupError) {
        console.error('Popup token acquisition failed:', popupError);
        throw new Error(`Token acquisition failed: ${popupError.message}`);
      }
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setMessage({ type: '', text: '' });

    try {
      // Get both backend and graph tokens
      const tokens = await getTokens();

      // Filter out empty recipients
      const emailData = {
        ...formData,
        to: formData.to.filter(r => r.email.trim()),
        cc: formData.cc.filter(r => r.email.trim()),
        bcc: formData.bcc.filter(r => r.email.trim())
      };

      // Validate required fields
      if (emailData.to.length === 0) {
        throw new Error('At least one recipient is required');
      }
      if (!emailData.subject.trim()) {
        throw new Error('Subject is required');
      }
      if (!emailData.body.trim()) {
        throw new Error('Email body is required');
      }

      // Send email with both tokens
      const response = await axios.post(
        `${apiConfig.baseUrl}/send-email`,
        emailData,
        {
          headers: {
            'Authorization': `Bearer ${tokens.backendToken}`,
            'X-Graph-Token': tokens.graphToken,
            'Content-Type': 'application/json'
          }
        }
      );

      setMessage({
        type: 'success',
        text: `✅ ${response.data.message} - ${response.data.details}`
      });

      // Reset form
      setFormData({
        to: [{ email: '', name: '' }],
        cc: [],
        bcc: [],
        subject: '',
        body: '',
        body_type: 'Text'
      });

    } catch (error) {
      console.error('Error sending email:', error);
      let errorMessage = 'Failed to send email';

      if (error.response?.data?.detail) {
        errorMessage = error.response.data.detail;

        // Check if it's a consent error
        if (errorMessage.includes('AADSTS65001') || errorMessage.includes('consent')) {
          setShowConsentHelper(true);
          errorMessage = 'API consent required. Please grant permissions using the helper below.';
        }
      } else if (error.message) {
        errorMessage = error.message;
      }

      setMessage({
        type: 'danger',
        text: `❌ ${errorMessage}`
      });
    } finally {
      setLoading(false);
    }
  };

  const renderRecipientInputs = (type, label) => (
    <Form.Group className="mb-3">
      <Form.Label>{label}</Form.Label>
      {formData[type].map((recipient, index) => (
        <Row key={index} className="mb-2">
          <Col md={6}>
            <Form.Control
              type="email"
              placeholder="Email address"
              value={recipient.email}
              onChange={(e) => handleRecipientChange(type, index, 'email', e.target.value)}
              required={type === 'to' && index === 0}
            />
          </Col>
          <Col md={4}>
            <Form.Control
              type="text"
              placeholder="Name (optional)"
              value={recipient.name}
              onChange={(e) => handleRecipientChange(type, index, 'name', e.target.value)}
            />
          </Col>
          <Col md={2}>
            {(type !== 'to' || index > 0) && (
              <Button
                variant="outline-danger"
                size="sm"
                onClick={() => removeRecipient(type, index)}
              >
                Remove
              </Button>
            )}
          </Col>
        </Row>
      ))}
      <Button
        variant="outline-secondary"
        size="sm"
        onClick={() => addRecipient(type)}
      >
        Add {label}
      </Button>
    </Form.Group>
  );

  return (
    <Card className="email-form">
      <Card.Header>
        <h3>📧 Compose Email</h3>
      </Card.Header>
      <Card.Body>
        <Form onSubmit={handleSubmit}>
          {renderRecipientInputs('to', 'To *')}
          {renderRecipientInputs('cc', 'CC')}
          {renderRecipientInputs('bcc', 'BCC')}

          <Form.Group className="mb-3">
            <Form.Label>Subject *</Form.Label>
            <Form.Control
              type="text"
              name="subject"
              value={formData.subject}
              onChange={handleInputChange}
              placeholder="Enter email subject"
              required
            />
          </Form.Group>

          <Form.Group className="mb-3">
            <Form.Label>Body Type</Form.Label>
            <Form.Select
              name="body_type"
              value={formData.body_type}
              onChange={handleInputChange}
            >
              <option value="Text">Plain Text</option>
              <option value="HTML">HTML</option>
            </Form.Select>
          </Form.Group>

          <Form.Group className="mb-3">
            <Form.Label>Email Body *</Form.Label>
            <Form.Control
              as="textarea"
              rows={8}
              name="body"
              value={formData.body}
              onChange={handleInputChange}
              placeholder="Enter your email content here..."
              required
            />
          </Form.Group>

          <div className="d-grid">
            <Button
              variant="primary"
              type="submit"
              disabled={loading}
              size="lg"
            >
              {loading ? (
                <>
                  <span className="loading-spinner me-2"></span>
                  Sending Email...
                </>
              ) : (
                '📤 Send Email'
              )}
            </Button>
          </div>
        </Form>

        {message.text && (
          <Alert variant={message.type} className="mt-3">
            {message.text}
          </Alert>
        )}

        {showConsentHelper && (
          <ConsentHelper />
        )}
      </Card.Body>
    </Card>
  );
}

export default EmailForm;
